[{"model": "admin.logentry", "pk": 1, "fields": {"action_time": "2025-05-29T10:32:03.045Z", "user": 3, "content_type": 12, "object_id": "1", "object_repr": "Survival", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 2, "fields": {"action_time": "2025-05-29T10:32:08.596Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "Bedrock", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 3, "fields": {"action_time": "2025-05-29T10:39:19.263Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "Raiden", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 4, "fields": {"action_time": "2025-05-29T10:39:26.442Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 5, "fields": {"action_time": "2025-05-29T10:39:31.214Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 6, "fields": {"action_time": "2025-05-29T10:39:42.332Z", "user": 3, "content_type": 14, "object_id": "1", "object_repr": "Windows", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 7, "fields": {"action_time": "2025-05-29T10:39:47.292Z", "user": 3, "content_type": 14, "object_id": "2", "object_repr": "Android", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 8, "fields": {"action_time": "2025-05-29T10:40:22.721Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main (***********:25556)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 9, "fields": {"action_time": "2025-05-29T10:40:55.924Z", "user": 3, "content_type": 15, "object_id": "2", "object_repr": "<PERSON><PERSON> (***********:25557)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 10, "fields": {"action_time": "2025-05-29T10:41:57.184Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 11, "fields": {"action_time": "2025-05-29T10:42:26.866Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 12, "fields": {"action_time": "2025-05-29T10:43:31.525Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 13, "fields": {"action_time": "2025-05-29T13:49:05.369Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "Bedrock", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 14, "fields": {"action_time": "2025-05-29T13:49:16.056Z", "user": 3, "content_type": 12, "object_id": "1", "object_repr": "Survival", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 15, "fields": {"action_time": "2025-05-29T13:49:41.012Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 16, "fields": {"action_time": "2025-05-29T13:49:54.241Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 17, "fields": {"action_time": "2025-05-29T13:50:55.947Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "<PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 18, "fields": {"action_time": "2025-05-29T19:19:37.424Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Port\", \"Query port\"]}}]"}}, {"model": "admin.logentry", "pk": 19, "fields": {"action_time": "2025-05-30T07:11:11.747Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main-Velocity (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 20, "fields": {"action_time": "2025-05-30T09:19:09.688Z", "user": 3, "content_type": 12, "object_id": "3", "object_repr": "Ranks", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 21, "fields": {"action_time": "2025-05-30T09:21:03.711Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "Iron rank 1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 22, "fields": {"action_time": "2025-05-30T19:52:38.411Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "Iron rank 1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Description\", \"Image\", \"Ucoin price\"]}}]"}}, {"model": "admin.logentry", "pk": 23, "fields": {"action_time": "2025-05-30T19:54:51.834Z", "user": 3, "content_type": 16, "object_id": "5", "object_repr": "rank-gold-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 24, "fields": {"action_time": "2025-05-30T19:55:17.309Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "rank-iron-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 25, "fields": {"action_time": "2025-05-30T19:58:04.737Z", "user": 3, "content_type": 16, "object_id": "6", "object_repr": "rank-dia-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 26, "fields": {"action_time": "2025-05-30T20:48:11.955Z", "user": 3, "content_type": 16, "object_id": "7", "object_repr": "rank-emral-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 27, "fields": {"action_time": "2025-05-30T20:49:47.716Z", "user": 3, "content_type": 16, "object_id": "8", "object_repr": "rank-fal-20d", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 28, "fields": {"action_time": "2025-05-30T21:00:45.128Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 29, "fields": {"action_time": "2025-05-30T21:01:19.352Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 30, "fields": {"action_time": "2025-05-30T21:02:29.503Z", "user": 3, "content_type": 15, "object_id": "2", "object_repr": "ucraft-lobby (***********:25557)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 31, "fields": {"action_time": "2025-05-30T21:03:07.456Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "ucraft-main (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 32, "fields": {"action_time": "2025-05-30T21:03:49.183Z", "user": 3, "content_type": 15, "object_id": "3", "object_repr": "ucraft-survival (***********:22557)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 33, "fields": {"action_time": "2025-05-30T21:04:19.543Z", "user": 3, "content_type": 15, "object_id": "4", "object_repr": "ucraft-bedrock (***********:22558)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 34, "fields": {"action_time": "2025-05-30T21:04:59.023Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 35, "fields": {"action_time": "2025-05-30T21:06:03.930Z", "user": 3, "content_type": 16, "object_id": "10", "object_repr": "survival-key-5k-rare", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 36, "fields": {"action_time": "2025-05-30T21:06:55.752Z", "user": 3, "content_type": 16, "object_id": "11", "object_repr": "survival-key-5k-money", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 37, "fields": {"action_time": "2025-05-30T21:08:35.505Z", "user": 3, "content_type": 16, "object_id": "12", "object_repr": "survival-cube-5*5", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 38, "fields": {"action_time": "2025-05-30T21:10:17.833Z", "user": 3, "content_type": 16, "object_id": "13", "object_repr": "survival-cube-15*15", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 39, "fields": {"action_time": "2025-05-30T21:11:40.138Z", "user": 3, "content_type": 16, "object_id": "14", "object_repr": "survival-key-5k-spawner", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 40, "fields": {"action_time": "2025-05-30T22:03:30.461Z", "user": 3, "content_type": 16, "object_id": "15", "object_repr": "survival-key-5k-legendary", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 41, "fields": {"action_time": "2025-05-30T22:04:15.696Z", "user": 3, "content_type": 16, "object_id": "16", "object_repr": "survival-key-5k-aegis", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 42, "fields": {"action_time": "2025-05-30T22:04:59.856Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\", \"Description\", \"Enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 43, "fields": {"action_time": "2025-05-30T22:05:54.589Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 44, "fields": {"action_time": "2025-05-30T22:06:11.059Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Enabled\", \"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 45, "fields": {"action_time": "2025-05-30T22:06:33.191Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 46, "fields": {"action_time": "2025-05-30T22:07:05.028Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 47, "fields": {"action_time": "2025-05-30T22:07:11.489Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 48, "fields": {"action_time": "2025-05-30T22:11:28.047Z", "user": 3, "content_type": 15, "object_id": "4", "object_repr": "ucraft-bedrwars (***********:22558)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 49, "fields": {"action_time": "2025-05-30T22:11:38.600Z", "user": 3, "content_type": 16, "object_id": "17", "object_repr": "bedwars-private-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 50, "fields": {"action_time": "2025-05-30T22:14:43.598Z", "user": 3, "content_type": 16, "object_id": "18", "object_repr": "bedwars-token-50k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 51, "fields": {"action_time": "2025-05-30T22:15:33.813Z", "user": 3, "content_type": 16, "object_id": "19", "object_repr": "bedwars-token-100k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 52, "fields": {"action_time": "2025-05-30T22:15:55.829Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 53, "fields": {"action_time": "2025-05-30T22:16:48.721Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 54, "fields": {"action_time": "2025-06-09T16:20:22.828Z", "user": 3, "content_type": 16, "object_id": "19", "object_repr": "bedwars-token-100k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 55, "fields": {"action_time": "2025-06-09T16:25:46.796Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 56, "fields": {"action_time": "2025-06-09T16:26:17.983Z", "user": 3, "content_type": 16, "object_id": "18", "object_repr": "bedwars-token-50k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 57, "fields": {"action_time": "2025-06-09T16:26:47.509Z", "user": 3, "content_type": 16, "object_id": "17", "object_repr": "bedwars-private-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 58, "fields": {"action_time": "2025-06-09T16:27:07.604Z", "user": 3, "content_type": 16, "object_id": "16", "object_repr": "survival-key-5k-aegis", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 59, "fields": {"action_time": "2025-06-09T16:27:33.016Z", "user": 3, "content_type": 16, "object_id": "15", "object_repr": "survival-key-5k-legendary", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 60, "fields": {"action_time": "2025-06-09T16:27:56.907Z", "user": 3, "content_type": 16, "object_id": "14", "object_repr": "survival-key-5k-spawner", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 61, "fields": {"action_time": "2025-06-09T16:28:21.478Z", "user": 3, "content_type": 16, "object_id": "13", "object_repr": "survival-cube-15*15", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 62, "fields": {"action_time": "2025-06-09T16:28:42.365Z", "user": 3, "content_type": 16, "object_id": "12", "object_repr": "survival-cube-5*5", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 63, "fields": {"action_time": "2025-06-09T16:29:09.464Z", "user": 3, "content_type": 16, "object_id": "11", "object_repr": "survival-key-5k-money", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 64, "fields": {"action_time": "2025-06-09T16:30:01.178Z", "user": 3, "content_type": 16, "object_id": "10", "object_repr": "survival-key-5k-rare", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 65, "fields": {"action_time": "2025-06-09T16:30:21.367Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 66, "fields": {"action_time": "2025-06-09T16:30:46.973Z", "user": 3, "content_type": 16, "object_id": "8", "object_repr": "rank-fal-20d", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 67, "fields": {"action_time": "2025-06-09T16:31:09.038Z", "user": 3, "content_type": 16, "object_id": "7", "object_repr": "rank-emral-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 68, "fields": {"action_time": "2025-06-09T16:31:55.251Z", "user": 3, "content_type": 16, "object_id": "6", "object_repr": "rank-dia-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 69, "fields": {"action_time": "2025-06-09T16:32:32.823Z", "user": 3, "content_type": 16, "object_id": "5", "object_repr": "rank-gold-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 70, "fields": {"action_time": "2025-06-09T16:33:07.086Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "rank-iron-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 71, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "13", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 72, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "12", "object_repr": "sinazk - Item 3 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 73, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "11", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 74, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "10", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 75, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "9", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 76, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "8", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 77, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "7", "object_repr": "Sinazk - Item 3 [finished]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 78, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "6", "object_repr": "raiden - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 79, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "5", "object_repr": "raiden - Item 2 [command_failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 80, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "4", "object_repr": "raiden - Item 2 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 81, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "3", "object_repr": "raiden - Item 3 [finished]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 82, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "2", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 83, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "1", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 84, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 85, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 86, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 87, "fields": {"action_time": "2025-06-09T16:37:18.947Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 88, "fields": {"action_time": "2025-06-09T16:38:31.045Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Description\", \"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 89, "fields": {"action_time": "2025-06-09T16:40:03.080Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "<PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add user", "content_type": 4, "codename": "add_user"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change user", "content_type": 4, "codename": "change_user"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete user", "content_type": 4, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view user", "content_type": 4, "codename": "view_user"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add content type", "content_type": 5, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change content type", "content_type": 5, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete content type", "content_type": 5, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view content type", "content_type": 5, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add session", "content_type": 6, "codename": "add_session"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change session", "content_type": 6, "codename": "change_session"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete session", "content_type": 6, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view session", "content_type": 6, "codename": "view_session"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add Scheduled task", "content_type": 7, "codename": "add_schedule"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change Scheduled task", "content_type": 7, "codename": "change_schedule"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete Scheduled task", "content_type": 7, "codename": "delete_schedule"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view Scheduled task", "content_type": 7, "codename": "view_schedule"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add task", "content_type": 8, "codename": "add_task"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change task", "content_type": 8, "codename": "change_task"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete task", "content_type": 8, "codename": "delete_task"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view task", "content_type": 8, "codename": "view_task"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add Failed task", "content_type": 9, "codename": "add_failure"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change Failed task", "content_type": 9, "codename": "change_failure"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete Failed task", "content_type": 9, "codename": "delete_failure"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view Failed task", "content_type": 9, "codename": "view_failure"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add Successful task", "content_type": 10, "codename": "add_success"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change Successful task", "content_type": 10, "codename": "change_success"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete Successful task", "content_type": 10, "codename": "delete_success"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view Successful task", "content_type": 10, "codename": "view_success"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add Queued task", "content_type": 11, "codename": "add_ormq"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change Queued task", "content_type": 11, "codename": "change_ormq"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete Queued task", "content_type": 11, "codename": "delete_ormq"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view Queued task", "content_type": 11, "codename": "view_ormq"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add category", "content_type": 12, "codename": "add_category"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change category", "content_type": 12, "codename": "change_category"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete category", "content_type": 12, "codename": "delete_category"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view category", "content_type": 12, "codename": "view_category"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add content creator", "content_type": 13, "codename": "add_contentcreator"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change content creator", "content_type": 13, "codename": "change_contentcreator"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete content creator", "content_type": 13, "codename": "delete_contentcreator"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view content creator", "content_type": 13, "codename": "view_contentcreator"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add download link", "content_type": 14, "codename": "add_downloadlink"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change download link", "content_type": 14, "codename": "change_downloadlink"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete download link", "content_type": 14, "codename": "delete_downloadlink"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view download link", "content_type": 14, "codename": "view_downloadlink"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add minecraft server", "content_type": 15, "codename": "add_minecraftserver"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change minecraft server", "content_type": 15, "codename": "change_minecraftserver"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete minecraft server", "content_type": 15, "codename": "delete_minecraftserver"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view minecraft server", "content_type": 15, "codename": "view_minecraftserver"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add item", "content_type": 16, "codename": "add_item"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change item", "content_type": 16, "codename": "change_item"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete item", "content_type": 16, "codename": "delete_item"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view item", "content_type": 16, "codename": "view_item"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add purchase", "content_type": 17, "codename": "add_purchase"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change purchase", "content_type": 17, "codename": "change_purchase"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete purchase", "content_type": 17, "codename": "delete_purchase"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view purchase", "content_type": 17, "codename": "view_purchase"}}, {"model": "auth.permission", "pk": 69, "fields": {"name": "Can add purchase item", "content_type": 18, "codename": "add_purchaseitem"}}, {"model": "auth.permission", "pk": 70, "fields": {"name": "Can change purchase item", "content_type": 18, "codename": "change_purchaseitem"}}, {"model": "auth.permission", "pk": 71, "fields": {"name": "Can delete purchase item", "content_type": 18, "codename": "delete_purchaseitem"}}, {"model": "auth.permission", "pk": 72, "fields": {"name": "Can view purchase item", "content_type": 18, "codename": "view_purchaseitem"}}, {"model": "auth.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$1000000$803Xa7g5iExssUJ6cbot41$LCyA4u/s8E5AffPIW+Xc2wMqek3ue2l1obxuq/WF0PU=", "last_login": null, "is_superuser": true, "username": "sinazk", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-28T18:43:55.659Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$1000000$aw3XV80Fh1t6le4YgeuZjg$a8z0YZsNH4Kqy78LI4YL0PTiOGMxHG1JtOX1RTUCGuM=", "last_login": "2025-05-28T18:45:24.078Z", "is_superuser": true, "username": "Sinazk", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-28T18:45:00.156Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 3, "fields": {"password": "pbkdf2_sha256$1000000$VXz48WQCKXcbDnhSx23HNW$AHaqoA2vODL1As4/Z7svoyYCU60DJeGn3tLIs+tmDxE=", "last_login": "2025-06-16T01:02:47.053Z", "is_superuser": true, "username": "navid", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-29T10:31:02.847Z", "groups": [], "user_permissions": []}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "auth", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "django_q", "model": "schedule"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "django_q", "model": "task"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "django_q", "model": "failure"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "django_q", "model": "success"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "django_q", "model": "ormq"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "shop", "model": "category"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "shop", "model": "contentcreator"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "shop", "model": "downloadlink"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "shop", "model": "minecraftserver"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "shop", "model": "item"}}, {"model": "contenttypes.contenttype", "pk": 17, "fields": {"app_label": "shop", "model": "purchase"}}, {"model": "contenttypes.contenttype", "pk": 18, "fields": {"app_label": "shop", "model": "purchaseitem"}}, {"model": "sessions.session", "pk": "8z9t5qpdiulj75ppfceak45gkr4nqqth", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uKvsz:44avAhGPOvNHLj_RDZ3tWXK9EXENzU2fiLBvt0xvB4g", "expire_date": "2025-06-13T09:18:45.916Z"}}, {"model": "sessions.session", "pk": "is8amkyhy3ykovk5307fzlwdbtsdncws", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uKaXb:uZzFtKSWRxPl0Xbk3AdJxPZcHpAID1hmDBy6ehWeYII", "expire_date": "2025-06-12T10:31:15.640Z"}}, {"model": "sessions.session", "pk": "j7ju0vbr0mn9gbz91kztfo9mbrbuggop", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uL3xF:sKkRvN207pRA7ERDrX6L1wqp-MLjTkmHF8fhBoQlHCc", "expire_date": "2025-06-13T17:55:41.058Z"}}, {"model": "sessions.session", "pk": "rhs11jhpqr4lo0c9ur6jn51jb4gl1q13", "fields": {"session_data": ".eJxVjMEOwiAQRP-FsyGwgGs9eu83kGUBqRqalPZk_Hdp0oMe5jDzZuYtPG1r8VtLi5-iuAoQp98sED9T3UF8UL3Pkue6LlOQe0UetMlxjul1O7p_B4Va6esLaGLFWkFQGKKF7BJCJEbDedCIWZ3jYF1yRoNiA2wpO4NKd98lPl_XwDbn:1uKLmG:X-ryAsLjZLzkXbhdXwTrgcZjA-RxdUiQ4xJR3jwZpWI", "expire_date": "2025-06-11T18:45:24.086Z"}}, {"model": "sessions.session", "pk": "xn7vdx3z8jr2jbcnofgc6e9u7990j4d0", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uQyFL:elReP4sNweSpdUJpWFhWDlmRx1Rm7OX_AFDHKjlIVbQ", "expire_date": "2025-06-30T01:02:47.060Z"}}, {"model": "django_q.task", "pk": "012ba51833d34b68a7a13c580f363ccc", "fields": {"name": "nebraska-seventeen-illinois-sixteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:11:29.244Z", "stopped": "2025-06-15T15:11:29.508Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "01c1204f479f4f159b2a253b1002cb0b", "fields": {"name": "pip-mango-pip-spring", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:01:18.028Z", "stopped": "2025-06-16T01:01:18.234Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "027079ba24244cf18961845632540e10", "fields": {"name": "pluto-rugby-edward-leopard", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:41:12.545Z", "stopped": "2025-06-15T13:41:12.780Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "02a4ed9c820941258ab9f7eb17d914be", "fields": {"name": "sweet-violet-dakota-indigo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:56:26.270Z", "stopped": "2025-06-15T22:56:26.544Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "046f6ccc82d24520b9339bdcc33fb052", "fields": {"name": "snake-double-sodium-california", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:36:26.374Z", "stopped": "2025-06-15T17:36:26.517Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "05c3d1bb3d5d41f1841788ab314dc955", "fields": {"name": "paris-spaghetti-pizza-timing", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:31:25.781Z", "stopped": "2025-06-15T09:31:26.016Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0820fd55ba4a475aad8bdb16c697cb23", "fields": {"name": "magnesium-victor-venus-river", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:26:24.858Z", "stopped": "2025-06-15T09:26:25.014Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "082edb25c3754a1580d810a0be70a620", "fields": {"name": "table-utah-uranus-fix", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:41:35.360Z", "stopped": "2025-06-15T07:41:35.554Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "08edf0415f7f4ee7aebb0e9151b84b6c", "fields": {"name": "glucose-michigan-mike-solar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:36:07.975Z", "stopped": "2025-06-15T10:36:08.191Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0b6f71b29cef45858fa3cd52ab198859", "fields": {"name": "oregon-princess-princess-india", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:36:15.017Z", "stopped": "2025-06-15T16:36:15.185Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0d421f24b19a486ca9c53156ce125d23", "fields": {"name": "lactose-avocado-asparagus-ohio", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:01:27.848Z", "stopped": "2025-06-15T07:01:28.136Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0d95ec1299a445b2988d2fbb7b752a83", "fields": {"name": "iowa-skylark-maine-oregon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:56:34.160Z", "stopped": "2025-06-15T12:56:34.302Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0d98c5779f914b40836520471d90fd0e", "fields": {"name": "coffee-angel-maine-low", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:46:13.837Z", "stopped": "2025-06-15T21:46:14.017Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0f12d736f86c49e8917237f53e6c8d52", "fields": {"name": "yankee-double-carolina-mississippi", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:56:30.495Z", "stopped": "2025-06-15T09:56:30.720Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0fa1a1e650cd417fb80d8dff2547adf8", "fields": {"name": "utah-oklahoma-sink-earth", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:51:33.792Z", "stopped": "2025-06-15T20:51:33.943Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "10ca701d93864070beaf361b3fb15509", "fields": {"name": "quiet-charlie-eighteen-spring", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:46:35.738Z", "stopped": "2025-06-15T15:46:35.967Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "11b7866ed0e840b7bdbc53a56c2ff3a2", "fields": {"name": "foxtrot-south-winter-batman", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:26:36.163Z", "stopped": "2025-06-15T10:26:36.387Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "13d4c50d61754e1490fd8a7571776500", "fields": {"name": "avocado-foxtrot-montana-india", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:11:06.882Z", "stopped": "2025-06-15T13:11:07.022Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1484c162ec65454ea7e58470c7f6031b", "fields": {"name": "diet-mango-queen-cold", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:46:13.456Z", "stopped": "2025-06-15T13:46:13.699Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1561db6ba8784d419cd1c71764d36485", "fields": {"name": "shade-tennis-nitrogen-green", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:11:33.034Z", "stopped": "2025-06-15T18:11:33.188Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "15b08b1d3a3349c18cda46d42c1feeec", "fields": {"name": "diet-maine-shade-delaware", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:11:33.372Z", "stopped": "2025-06-15T10:11:33.559Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "161ef88e48b24dff82051b0cb964e2d7", "fields": {"name": "low-fix-mockingbird-uranus", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:26:13.669Z", "stopped": "2025-06-15T08:26:13.775Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "169d2d90bd9d45e9a627fc67d9ee2eb5", "fields": {"name": "early-mirror-bravo-paris", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:01:35.685Z", "stopped": "2025-06-15T21:01:35.924Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "18475a65f9c54e0f940ff311541bde2a", "fields": {"name": "hydrogen-louisiana-lima-robin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:11:22.051Z", "stopped": "2025-06-15T09:11:22.322Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1b989e2a9d9943078deac759e2a77a3c", "fields": {"name": "earth-equal-wolfram-alaska", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:31:36.889Z", "stopped": "2025-06-15T18:31:36.978Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1be37a38b7594e3ea161a64d94a7ce10", "fields": {"name": "robert-north-comet-victor", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:51:29.154Z", "stopped": "2025-06-15T17:51:29.305Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1c280d34fec44962a35db25a513a6c9f", "fields": {"name": "august-bakerloo-black-neptune", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:01:08.451Z", "stopped": "2025-06-15T16:01:08.723Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1cc4fc7f90254d9b8046726e12371e26", "fields": {"name": "hawaii-india-ten-butter", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:16:22.571Z", "stopped": "2025-06-15T17:16:22.645Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1d1b78f1ac0c4b7fa65a9035ebf02d76", "fields": {"name": "mobile-mountain-happy-equal", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:56:18.809Z", "stopped": "2025-06-15T16:56:18.955Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1d7b5e2d240f4992b6d2cef09922c126", "fields": {"name": "beryllium-virginia-harry-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:56:22.882Z", "stopped": "2025-06-15T11:56:23.196Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1da818fc98bb438ab1644186ed98c776", "fields": {"name": "fruit-vegan-batman-shade", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:36:33.894Z", "stopped": "2025-06-15T15:36:34.157Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1e6f03a6adbf4fce98d8d0d3bf51badb", "fields": {"name": "steak-moon-fifteen-mobile", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:56:23.204Z", "stopped": "2025-06-15T19:56:23.464Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1e89577d61ed47a0a83171e8494a00db", "fields": {"name": "rugby-johnny-ack-table", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:26:20.829Z", "stopped": "2025-06-15T14:26:21.105Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "20d095d7dd6f4e45822a7d0165bb1770", "fields": {"name": "potato-river-steak-river", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:26:09.715Z", "stopped": "2025-06-15T13:26:09.822Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "218a2688b8054183adbcf22224896cb5", "fields": {"name": "angel-magazine-september-lactose", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:06:13.512Z", "stopped": "2025-06-15T19:06:13.721Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "21c21402c4ad41369ede3178b43a752d", "fields": {"name": "william-california-arkansas-summer", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:51:25.382Z", "stopped": "2025-06-15T22:51:25.673Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2219d9f332b94266a45d744f6addae97", "fields": {"name": "bakerloo-pip-seven-missouri", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:06:09.972Z", "stopped": "2025-06-15T08:06:10.182Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "22c4bf10011144db9473081a99d5654c", "fields": {"name": "floor-mobile-william-ink", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:41:23.599Z", "stopped": "2025-06-15T22:41:23.792Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2339026cf85345afb3cf8b69fe544bb6", "fields": {"name": "lactose-failed-north-grey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:41:34.808Z", "stopped": "2025-06-15T15:41:34.956Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2385c65e03db4e91bc9cdba313b8019e", "fields": {"name": "emma-april-hamper-don", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:16:11.806Z", "stopped": "2025-06-15T08:16:12.040Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2627016fe16f4718bf94636b164df976", "fields": {"name": "kansas-speaker-cat-steak", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:16:34.315Z", "stopped": "2025-06-15T10:16:34.423Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "265c2585b1454904a1b9b27a93fa08f0", "fields": {"name": "ten-river-spaghetti-monkey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:01:24.186Z", "stopped": "2025-06-15T20:01:24.391Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2725153881824b39a4b27ff3d7a96f94", "fields": {"name": "dakota-kilo-nevada-maryland", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:26:21.436Z", "stopped": "2025-06-15T06:26:21.690Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "27dfb4b339e341f18c0360785b0dd13e", "fields": {"name": "zulu-jig-nebraska-river", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:06:28.808Z", "stopped": "2025-06-15T07:06:28.905Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2820254584a94eb1b0034357d55ca951", "fields": {"name": "item-spring-oregon-kilo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:21:31.682Z", "stopped": "2025-06-15T07:21:31.980Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2e0bbd654c814e64ae701f8d30c721ab", "fields": {"name": "yellow-uniform-four-lo<PERSON>siana", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:21:30.700Z", "stopped": "2025-06-15T23:21:31.007Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "305b4e3d29854a90b37de99ac1505c37", "fields": {"name": "blue-lamp-rugby-wyoming", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:31:11.240Z", "stopped": "2025-06-15T05:31:11.410Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "328d3618f5714e6e98c89ba0227425f2", "fields": {"name": "monkey-one-johnny-maine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:16:19.632Z", "stopped": "2025-06-15T06:16:19.877Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "33225b8d28bc421690e88323d233b6f2", "fields": {"name": "sink-saturn-table-orange", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:56:07.493Z", "stopped": "2025-06-15T15:56:07.584Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "33cf3da811914e11a3a2fbe5ca0a1713", "fields": {"name": "jersey-three-carbon-shade", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:31:10.656Z", "stopped": "2025-06-15T13:31:10.871Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "33df06d5c2d340cc92710a2f3ded41a4", "fields": {"name": "enemy-nitrogen-single-double", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:41:12.962Z", "stopped": "2025-06-15T21:41:13.115Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "342a87f675504f29bf6a4658e8d1f867", "fields": {"name": "kansas-fifteen-snake-oranges", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:21:23.925Z", "stopped": "2025-06-15T09:21:24.029Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "344967b8798447aa84d1f8debbc50b61", "fields": {"name": "ohio-bacon-kitten-berlin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:46:35.100Z", "stopped": "2025-06-15T23:46:35.316Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3470ba4ace7a4f41bd9b42ecda91b672", "fields": {"name": "purple-robert-mexico-mobile", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:01:19.778Z", "stopped": "2025-06-15T17:01:20.033Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "368db81dac50454598b8433729885ae6", "fields": {"name": "california-yankee-glucose-butter", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:01:31.468Z", "stopped": "2025-06-15T10:01:31.765Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3715bebb02c5460895405c05cf377c1f", "fields": {"name": "coffee-michigan-mirror-nine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T04:56:35.069Z", "stopped": "2025-06-15T04:56:35.218Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3740adec2a584938b7e60302a4eb1b1d", "fields": {"name": "early-alaska-friend-floor", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:46:28.625Z", "stopped": "2025-06-15T09:46:28.748Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3802f88f59e74e01b4b1382dc68480c7", "fields": {"name": "fourteen-washington-bacon-angel", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:46:16.916Z", "stopped": "2025-06-15T16:46:17.118Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3b7fc91b0738451897f271eb653e48c5", "fields": {"name": "music-illinois-zulu-summer", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:11:18.619Z", "stopped": "2025-06-15T06:11:18.832Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3bebaa6f2867420a88d6a46feb8aff1e", "fields": {"name": "beryllium-april-muppet-magnesium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:41:20.271Z", "stopped": "2025-06-15T19:41:20.435Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3c29e538cc334fe5bd21fca45cca5123", "fields": {"name": "six-golf-shade-two", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:31:33.525Z", "stopped": "2025-06-15T07:31:33.606Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3c979d3fe6634f038ec54d63cce02a99", "fields": {"name": "arkansas-sixteen-ink-november", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:01:16.227Z", "stopped": "2025-06-15T14:01:16.334Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3ced9a98e8b0423bb3ed6f463f4e0600", "fields": {"name": "stairway-carolina-lemon-lemon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:41:16.453Z", "stopped": "2025-06-15T08:41:16.688Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3d61733e51a3401491756413069c2133", "fields": {"name": "bakerloo-oranges-moon-mexico", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:06:24.778Z", "stopped": "2025-06-15T12:06:24.919Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3eb28970898f4055bb64c7102e87183b", "fields": {"name": "lake-beer-arizona-mike", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:21:08.772Z", "stopped": "2025-06-15T13:21:08.911Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3f46b196387a4b219ec2953d6b453262", "fields": {"name": "hot-cola-white-low", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:21:23.563Z", "stopped": "2025-06-15T17:21:23.735Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "40800af36cc2423dad63d14b4f9ba979", "fields": {"name": "echo-speaker-glucose-alpha", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:56:11.616Z", "stopped": "2025-06-15T18:56:11.907Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "430d86399dd04e3aa499d2b2a85adcfa", "fields": {"name": "washington-echo-high-steak", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:16:08.547Z", "stopped": "2025-06-15T05:16:08.639Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "438fe281bbbd426bbc0243c076a1da1a", "fields": {"name": "pip-dakota-eleven-blossom", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:51:29.568Z", "stopped": "2025-06-15T09:51:29.770Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "440d3c0e65cd48ffb6b432953d15422c", "fields": {"name": "lithium-carbon-orange-fish", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:01:23.818Z", "stopped": "2025-06-15T12:01:24.030Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4601a19978694e99826c16db02354299", "fields": {"name": "magazine-single-cat-vermont", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:36:13.719Z", "stopped": "2025-06-16T00:36:13.923Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "463fb354e1184f549905dfc25c0d8a0f", "fields": {"name": "north-twelve-maine-steak", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:21:34.958Z", "stopped": "2025-06-15T18:21:35.078Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "465c00080ee349148202803f729c3df5", "fields": {"name": "batman-pizza-enemy-chicken", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:16:27.030Z", "stopped": "2025-06-15T20:16:27.301Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "47e4b7dd1072435fb2a06950e039dd59", "fields": {"name": "sixteen-low-comet-high", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:41:13.081Z", "stopped": "2025-06-15T05:41:13.363Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4dee64a85b3647ab8c195d396ed23046", "fields": {"name": "kansas-maine-washington-skylark", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:56:17.157Z", "stopped": "2025-06-16T00:56:17.311Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4f82fc88a1f942f0a972687ad113ee3d", "fields": {"name": "river-vegan-sixteen-bakerloo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:06:09.386Z", "stopped": "2025-06-15T16:06:09.696Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "50c0ede8b031424e90ade60ca59926f5", "fields": {"name": "muppet-mississippi-sweet-violet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:46:09.704Z", "stopped": "2025-06-15T18:46:09.885Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "523cd0cbf02b44939058e0c778505706", "fields": {"name": "lake-aspen-violet-lima", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:46:09.846Z", "stopped": "2025-06-15T10:46:10.023Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "52a614a2c35345b8bb9f24168f66328a", "fields": {"name": "fanta-july-black-fruit", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:01:35.956Z", "stopped": "2025-06-15T05:01:36.052Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "542516322d6b40d2a1e30f5d1bca8a1a", "fields": {"name": "golf-steak-bacon-failed", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:26:17.322Z", "stopped": "2025-06-15T11:26:17.568Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "56136cfcd07241b3a1f8d6dbe50a7a24", "fields": {"name": "stream-friend-glucose-pluto", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:41:27.299Z", "stopped": "2025-06-15T17:41:27.481Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5723e2b007b74f8eb43c8e922e07a6d5", "fields": {"name": "chicken-kilo-mountain-harry", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:56:30.106Z", "stopped": "2025-06-15T17:56:30.343Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "57bd660fc417436dba4a69ea8e847954", "fields": {"name": "march-yellow-kitten-coffee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:26:28.484Z", "stopped": "2025-06-15T12:26:28.785Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "57c2694896cb44ffa6e3423a28ecd52f", "fields": {"name": "purple-undress-eleven-emma", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:51:14.902Z", "stopped": "2025-06-15T05:51:15.131Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "57ce4f6014014f2c86709ab96b14548b", "fields": {"name": "queen-lamp-stream-nuts", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:51:33.229Z", "stopped": "2025-06-15T12:51:33.366Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5a0ad47aac024e89ab1a69f8030e3f87", "fields": {"name": "zebra-jersey-cardinal-quebec", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:16:19.213Z", "stopped": "2025-06-15T22:16:19.360Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5a38026352694cf9a7354ae31c61b082", "fields": {"name": "alaska-missouri-eight-freddie", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:41:27.665Z", "stopped": "2025-06-15T09:41:27.926Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5b34d7273cb64ad6ac0e6ac73a14a824", "fields": {"name": "november-black-avocado-ohio", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:41:15.960Z", "stopped": "2025-06-15T16:41:16.077Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5bd57239c0554c6daff1d2852ea24cfc", "fields": {"name": "sierra-purple-arkansas-eighteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:46:36.320Z", "stopped": "2025-06-15T07:46:36.591Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5cdeba176c5f4fe09cc4d1ae941c4832", "fields": {"name": "dakota-nineteen-coffee-west", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:26:28.945Z", "stopped": "2025-06-15T20:26:29.224Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6051bd691dee4774aa6cedbc2c77c4b1", "fields": {"name": "uncle-saturn-nebraska-cup", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:31:18.262Z", "stopped": "2025-06-15T11:31:18.468Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "607ce670a90e4819b823ea4bd5cbfa52", "fields": {"name": "mike-winner-edward-queen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:36:24.050Z", "stopped": "2025-06-16T01:36:24.201Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "60ec85b3f9e04e11896c0ef42c41e7d2", "fields": {"name": "robert-fish-carbon-hamper", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:16:30.743Z", "stopped": "2025-06-15T07:16:30.970Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "61047309ccd34a86a0ff02135fb6343c", "fields": {"name": "carbon-stairway-foxtrot-triple", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:41:24.908Z", "stopped": "2025-06-16T01:41:25.199Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "611e457928aa4ef3893b4b9542614d46", "fields": {"name": "fifteen-enemy-juliet-cola", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:21:27.989Z", "stopped": "2025-06-15T20:21:28.129Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6125e6db44b7458e9c9a10bb992519bc", "fields": {"name": "lima-east-pasta-bluebird", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:56:26.440Z", "stopped": "2025-06-15T14:56:26.736Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "623f091d7fbb435e98c1767fd218c984", "fields": {"name": "floor-tango-friend-may", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:01:20.197Z", "stopped": "2025-06-15T09:01:20.465Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "62ddfcb00b6243ce8bac92f116a9b171", "fields": {"name": "whiskey-two-fillet-magazine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:21:16.361Z", "stopped": "2025-06-15T11:21:16.587Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "62ff5b2d5411434bb12346073a252052", "fields": {"name": "venus-fourteen-stairway-july", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:51:25.518Z", "stopped": "2025-06-15T14:51:25.731Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "652384b7c40049c2a650cc5673f17499", "fields": {"name": "lake-freddie-juliet-kansas", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:16:15.413Z", "stopped": "2025-06-15T19:16:15.545Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "67d8dbeba62f4728b470d8580738a1b4", "fields": {"name": "mobile-undress-fillet-colorado", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:41:31.326Z", "stopped": "2025-06-15T12:41:31.577Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "685397b573794adbb1c76c47059640db", "fields": {"name": "bluebird-nine-high-arizona", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:06:32.428Z", "stopped": "2025-06-15T10:06:32.656Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "69eca78c0ac64a20ba7e70c3e30324c8", "fields": {"name": "pennsylvania-oranges-lemon-aspen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:56:15.307Z", "stopped": "2025-06-15T13:56:15.439Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "69ed12efff584011bc3b7966ddfa805a", "fields": {"name": "lima-winner-neptune-yellow", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:06:28.323Z", "stopped": "2025-06-15T15:06:28.520Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6a7bd5b34ac549a59c7421d91efc8265", "fields": {"name": "lemon-india-bacon-angel", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:16:07.848Z", "stopped": "2025-06-15T13:16:07.984Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6b496538e3254491af03e6ff36eb9ad2", "fields": {"name": "magazine-beer-quebec-muppet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:36:30.885Z", "stopped": "2025-06-15T20:36:31.023Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6ba1dc5876a44bf4bcd6282236e2e1cd", "fields": {"name": "foxtrot-july-hydrogen-wisconsin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:21:21.467Z", "stopped": "2025-06-16T01:21:21.680Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6c7a0f4fc7a943febc300c0b3ee07484", "fields": {"name": "green-stairway-minnesota-cup", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:46:24.572Z", "stopped": "2025-06-15T14:46:24.844Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6cbd25a689df434e8273684f9270ae1f", "fields": {"name": "gee-hawaii-summer-georgia", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:01:31.074Z", "stopped": "2025-06-15T18:01:31.312Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6d4820ee1cb440abb82f3adfd2919035", "fields": {"name": "venus-fifteen-king-undress", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:01:27.178Z", "stopped": "2025-06-15T23:01:27.443Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6e0ee36d0f0e4f85aa6911e4fbf79fd2", "fields": {"name": "video-snake-early-failed", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:51:18.333Z", "stopped": "2025-06-15T08:51:18.596Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "702c1665eadf40b7bc64f8f6fc1781bb", "fields": {"name": "dakota-seven-summer-speaker", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:16:10.297Z", "stopped": "2025-06-16T00:16:10.607Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "70911ffd5de74b30ba3e6930221615a9", "fields": {"name": "alaska-september-texas-mexico", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:21:12.258Z", "stopped": "2025-06-15T16:21:12.465Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "716c2a17e50746cfb46b71f8d1bb83d0", "fields": {"name": "cardinal-johnny-green-echo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:06:17.671Z", "stopped": "2025-06-15T06:06:17.865Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "71a02948a6e441b6b642d6f81cdc78ce", "fields": {"name": "pizza-quebec-ten-lamp", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:06:36.862Z", "stopped": "2025-06-15T05:06:37.029Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "71ab11bb3a5c488090dbb91d4868c30f", "fields": {"name": "kentucky-pennsylvania-violet-vermont", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:56:34.748Z", "stopped": "2025-06-15T20:56:34.834Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "72364b23da0746f19a3c179bee9797b2", "fields": {"name": "connecticut-jig-colorado-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:51:35.983Z", "stopped": "2025-06-15T23:51:36.268Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "748b13f9072d43abbe0e60fe1f6154bb", "fields": {"name": "beryllium-seven-cup-monkey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:46:32.273Z", "stopped": "2025-06-15T12:46:32.460Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "74c8b37020964b858ca63d8d599e253a", "fields": {"name": "magazine-leopard-nevada-table", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:31:32.958Z", "stopped": "2025-06-15T15:31:33.203Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "751164d3f3e041c79feb675e0ff551ce", "fields": {"name": "stairway-muppet-fourteen-ink", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:36:22.675Z", "stopped": "2025-06-15T14:36:22.991Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "75feb2a912934384b1e77506f780e3a8", "fields": {"name": "mockingbird-fish-charlie-eighteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:36:22.722Z", "stopped": "2025-06-15T22:36:22.943Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "776ff0fb420c4269b58dfe689f21f58e", "fields": {"name": "beer-ink-pennsylvania-sierra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:41:23.627Z", "stopped": "2025-06-15T14:41:23.895Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "783e35e4c0d9454b8c1aad7b75ceaa35", "fields": {"name": "nebraska-blossom-equal-ceiling", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:06:21.144Z", "stopped": "2025-06-15T09:06:21.386Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "78edb66eb4da4c80b983cc63bac4b210", "fields": {"name": "fourteen-ohio-muppet-tennis", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:31:29.429Z", "stopped": "2025-06-15T12:31:29.636Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7da3867f392b424db2d065802f5dcc8d", "fields": {"name": "coffee-butter-may-diet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:21:31.088Z", "stopped": "2025-06-15T15:21:31.324Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7f497d5df59745039fc586f34421a66b", "fields": {"name": "black-ceiling-dakota-oscar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:56:36.866Z", "stopped": "2025-06-15T23:56:36.979Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "801481c2f16340c9950c592f4226b8b0", "fields": {"name": "sweet-bulldog-violet-juliet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:51:17.864Z", "stopped": "2025-06-15T16:51:17.978Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "819914f0d0d54701a879693f0264a551", "fields": {"name": "yankee-fifteen-may-montana", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:16:18.991Z", "stopped": "2025-06-15T14:16:19.090Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "821948951bb146528aeb2beed71b3c94", "fields": {"name": "fish-sad-one-indigo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:06:36.052Z", "stopped": "2025-06-15T13:06:36.150Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "824f84ef32a64d91ab3888abef14d853", "fields": {"name": "oranges-fix-pizza-kentucky", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:11:29.774Z", "stopped": "2025-06-15T07:11:29.996Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "836718c658b64253a9151d07cbc80186", "fields": {"name": "edward-zulu-oranges-mars", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:36:26.724Z", "stopped": "2025-06-15T09:36:26.862Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "863b5743b994492d8e0b0e32b89cd45f", "fields": {"name": "oven-floor-river-pip", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:26:35.898Z", "stopped": "2025-06-15T18:26:36.199Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "86dc328ec21344288cd7e9bb5c18e1bd", "fields": {"name": "undress-rugby-arkansas-blue", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:21:12.738Z", "stopped": "2025-06-15T08:21:12.867Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "882ef3c8136949aaa493c2334f32db41", "fields": {"name": "nitrogen-network-single-uniform", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:11:19.753Z", "stopped": "2025-06-16T01:11:19.921Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8899933c13cf4ed0989d40185d4ccd5e", "fields": {"name": "sad-fanta-high-mango", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:21:09.322Z", "stopped": "2025-06-15T21:21:09.539Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "88e45c93809d4ed4b40f3bbe1eb9dd9e", "fields": {"name": "oklahoma-may-emma-two", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:01:07.649Z", "stopped": "2025-06-16T00:01:07.918Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8bb6752249eb41daa95f193f84611f86", "fields": {"name": "jig-echo-spaghetti-london", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:16:15.440Z", "stopped": "2025-06-15T11:16:15.711Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8bcc2cac74b74f7ba633303057781360", "fields": {"name": "sink-winter-muppet-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:31:23.182Z", "stopped": "2025-06-16T01:31:23.385Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8bd6d9f876184f33bf494d90fa9542db", "fields": {"name": "<PERSON><PERSON>-batman-comet-winner", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:26:32.609Z", "stopped": "2025-06-15T07:26:32.892Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8bf8b77290154dfdb0cf410e11b2e81e", "fields": {"name": "queen-double-december-golf", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:26:20.990Z", "stopped": "2025-06-15T22:26:21.219Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8c2195272a40495f89d26b25bb0a99e5", "fields": {"name": "carbon-london-beryllium-sodium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:36:12.044Z", "stopped": "2025-06-15T21:36:12.170Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "906cd97b37da46369ffffe4392e5c773", "fields": {"name": "hot-nitrogen-texas-seventeen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:26:31.577Z", "stopped": "2025-06-15T23:26:31.841Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9423e9dbc95f42cbb568f8344f5fdd7f", "fields": {"name": "maryland-carbon-bakerloo-potato", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:31:21.756Z", "stopped": "2025-06-15T14:31:21.888Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "95b0a1dc11e74418bf66efc54a559f18", "fields": {"name": "hamper-oxygen-whiskey-neptune", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:41:34.238Z", "stopped": "2025-06-15T23:41:34.418Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9656f0e7f2bc43e3aeda07817ccfb80f", "fields": {"name": "sweet-video-west-alpha", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:41:14.582Z", "stopped": "2025-06-16T00:41:14.709Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "975a0271857f4a43b1dbcbd981c9758d", "fields": {"name": "sad-whiskey-leopard-pizza", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:36:19.292Z", "stopped": "2025-06-15T19:36:19.570Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9975f767d0814603b7015a7ded87f8b1", "fields": {"name": "london-berlin-monkey-cat", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:11:25.715Z", "stopped": "2025-06-15T12:11:25.918Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9a8f66eeb541441db87df6933ad59218", "fields": {"name": "nitrogen-pasta-nuts-ink", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:31:25.427Z", "stopped": "2025-06-15T17:31:25.671Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9afaa4694e9d4125b1b3fa251b232197", "fields": {"name": "pasta-quiet-texas-missouri", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:31:18.308Z", "stopped": "2025-06-15T19:31:18.424Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9c6fa706d8364cc29b674ca5d31bfd64", "fields": {"name": "gee-hydrogen-beryllium-fruit", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:21:20.100Z", "stopped": "2025-06-15T22:21:20.320Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9ce33b074deb40a69eee6c8ae789c06f", "fields": {"name": "fifteen-equal-music-september", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T09:16:22.991Z", "stopped": "2025-06-15T09:16:23.117Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9ce4f0998cfb44d7820de15b82e797f3", "fields": {"name": "bakerloo-two-double-alabama", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:51:16.288Z", "stopped": "2025-06-16T00:51:16.434Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9d7f4dcd35394eefb7a646186e5c2d9e", "fields": {"name": "illinois-pasta-freddie-maine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:26:24.501Z", "stopped": "2025-06-15T17:26:24.736Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9e33c624a7eb418eba37b7889ea76c8e", "fields": {"name": "freddie-march-potato-tennessee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:46:13.999Z", "stopped": "2025-06-15T05:46:14.110Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9e68e2a3e6b4430ea09ae3d476ed25a9", "fields": {"name": "quiet-carbon-august-blossom", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:06:17.392Z", "stopped": "2025-06-15T22:06:17.599Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9ea3afbf74c94d938ec948ab8a2d3d7e", "fields": {"name": "crazy-india-one-two", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:11:28.950Z", "stopped": "2025-06-15T23:11:29.184Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9ef9dffbe16340239c516a53d8dfbf1b", "fields": {"name": "fifteen-triple-white-oklahoma", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:26:10.319Z", "stopped": "2025-06-15T05:26:10.434Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9f84c481774147f08eeb79dc93289add", "fields": {"name": "hot-maine-saturn-yankee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:56:19.253Z", "stopped": "2025-06-15T08:56:19.464Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a1512fcdaf8f4758a01692c64520291c", "fields": {"name": "batman-enemy-twelve-friend", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:36:19.174Z", "stopped": "2025-06-15T11:36:19.321Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a19d3810ab1246afb99dda0cb4bedc82", "fields": {"name": "berlin-friend-west-glucose", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:36:07.767Z", "stopped": "2025-06-15T18:36:07.961Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a475c3f638df49b6845dc369bcd70e96", "fields": {"name": "two-blue-kitten-whiskey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:31:07.019Z", "stopped": "2025-06-15T10:31:07.238Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a50c50f1b060467ea376fe62ae9670dd", "fields": {"name": "arizona-uranus-floor-muppet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:56:11.700Z", "stopped": "2025-06-15T10:56:11.934Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a6aac82bf3d04308921830406de447be", "fields": {"name": "violet-item-wyoming-kentucky", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:56:08.071Z", "stopped": "2025-06-15T07:56:08.337Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a78adb93c90e4890a7a0202541189e02", "fields": {"name": "ink-carbon-alanine-sierra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:26:32.005Z", "stopped": "2025-06-15T15:26:32.253Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a85878b6a90b4cd0892a2ba533b55d0e", "fields": {"name": "emma-emma-crazy-stream", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:36:33.348Z", "stopped": "2025-06-15T23:36:33.545Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a99d1ed131aa42f689a984b0f995dced", "fields": {"name": "xray-dakota-berlin-high", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:01:12.620Z", "stopped": "2025-06-15T11:01:12.763Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ab675ef05e714d2a87979c502d4090d1", "fields": {"name": "illinois-fix-tennis-connecticut", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:06:28.042Z", "stopped": "2025-06-15T23:06:28.212Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "abb99aadd5fa4ea6b8c3811b1d541d71", "fields": {"name": "charlie-sink-triple-charlie", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:46:21.029Z", "stopped": "2025-06-15T11:46:21.306Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ac5a0f3ac4fe47c2afd4c5aecf640be5", "fields": {"name": "seventeen-salami-bacon-finch", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:36:34.443Z", "stopped": "2025-06-15T07:36:34.685Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ac7d944ecf09474b8649c21b2099608e", "fields": {"name": "asparagus-skylark-black-grey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:11:10.887Z", "stopped": "2025-06-15T08:11:11.122Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ada6db3577ff437c9561990be17cdc53", "fields": {"name": "uncle-quiet-iowa-batman", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:01:16.740Z", "stopped": "2025-06-15T06:01:16.978Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "afca7f30442e4e89940b1f30ebd85765", "fields": {"name": "colorado-pasta-pizza-alabama", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:36:12.176Z", "stopped": "2025-06-15T05:36:12.263Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b288abbbf8ae44229f56dd99f4aa5d9b", "fields": {"name": "colorado-december-mississippi-neptune", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:56:15.808Z", "stopped": "2025-06-15T05:56:16.074Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b43467c779384ce3aceb4c6aa6998ef2", "fields": {"name": "cardinal-video-idaho-fifteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:06:25.138Z", "stopped": "2025-06-15T20:06:25.411Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b49312e6b9f1461ea359cf207e8891bc", "fields": {"name": "stairway-bacon-beryllium-tennessee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:06:32.064Z", "stopped": "2025-06-15T18:06:32.311Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b6769cffa9ed448b91efb6d84eb55a69", "fields": {"name": "oklahoma-minnesota-bacon-six", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:51:25.995Z", "stopped": "2025-06-15T06:51:26.173Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b6ad48fd738344ed841707a79c813870", "fields": {"name": "pennsylvania-happy-diet-golf", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:11:26.073Z", "stopped": "2025-06-15T20:11:26.234Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b6c41ebc6f164c178166b767b23687b8", "fields": {"name": "spaghetti-carpet-earth-robin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T07:51:07.154Z", "stopped": "2025-06-15T07:51:07.410Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b8994c6b1d7e4ffb8d459950749f8214", "fields": {"name": "apart-sweet-alabama-louisiana", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:51:10.782Z", "stopped": "2025-06-15T10:51:11.050Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ba3b507f41a6415fa9fe2f266eb9c433", "fields": {"name": "pennsylvania-wolfram-hot-cardinal", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:31:11.129Z", "stopped": "2025-06-15T21:31:11.250Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "baa466a3581d45e2bc62fef965b7c323", "fields": {"name": "tennessee-twelve-tango-delta", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:46:21.260Z", "stopped": "2025-06-15T19:46:21.507Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bdcd5030ccb442c7b2125b86b8abde47", "fields": {"name": "bacon-yellow-louisiana-mike", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:06:36.628Z", "stopped": "2025-06-15T21:06:36.812Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bdef84b65fd8486284ce5fa957294ece", "fields": {"name": "hydrogen-pasta-romeo-zebra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:46:24.483Z", "stopped": "2025-06-15T22:46:24.749Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bec9126ff4bf4ed4a4ef40e7d9e3bf3b", "fields": {"name": "artist-speaker-vermont-sad", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:26:17.319Z", "stopped": "2025-06-15T19:26:17.420Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c05fc32ae9c149ca97b8d0298373012b", "fields": {"name": "lamp-papa-summer-ten", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:16:26.627Z", "stopped": "2025-06-15T12:16:26.792Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c3d34b0f488d4ced89b1aad54a587fd3", "fields": {"name": "glucose-indigo-five-sad", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:41:08.737Z", "stopped": "2025-06-15T18:41:08.940Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c5934a4042e04be08b56f8e5289177dd", "fields": {"name": "helium-arkansas-saturn-beer", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:11:07.649Z", "stopped": "2025-06-15T05:11:07.852Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c64b61eeb02645bfb046f219cdb962da", "fields": {"name": "alanine-juliet-butter-social", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:06:08.542Z", "stopped": "2025-06-16T00:06:08.731Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c6620de4ff1c400dbb95bcb31905cd32", "fields": {"name": "fourteen-pennsylvania-carolina-twenty", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:36:15.538Z", "stopped": "2025-06-15T08:36:15.692Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c791e5fcb1594ccc8e40723c70057f49", "fields": {"name": "freddie-item-magnesium-fifteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:41:08.905Z", "stopped": "2025-06-15T10:41:09.106Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c79a624d76114018b964e7ca0b266134", "fields": {"name": "king-enemy-virginia-leopard", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:16:08.377Z", "stopped": "2025-06-15T21:16:08.535Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c8a372730bdc413691cf87ee415c9d29", "fields": {"name": "jig-nuts-earth-oven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:16:11.311Z", "stopped": "2025-06-15T16:16:11.401Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c8bcb1b4d2b241d2926e6c0bc5899d70", "fields": {"name": "kilo-whiskey-nine-october", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:36:23.266Z", "stopped": "2025-06-15T06:36:23.368Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c91ccdfbefdc40879e5ec663ada12467", "fields": {"name": "twenty-white-ink-freddie", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:01:16.476Z", "stopped": "2025-06-15T22:01:16.609Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c9d0debf8de0422597a6310c9c07bde9", "fields": {"name": "leopard-artist-aspen-south", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:46:15.432Z", "stopped": "2025-06-16T00:46:15.664Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "cba650acce3d4941bc2907a7a00b135e", "fields": {"name": "monkey-xray-double-undress", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:01:35.108Z", "stopped": "2025-06-15T13:01:35.297Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "cbe1c964356c4820a3c4556761a1e264", "fields": {"name": "may-delaware-golf-fish", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:51:14.701Z", "stopped": "2025-06-15T21:51:14.795Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d216e69ed1284ef4ac05db2d8215fb22", "fields": {"name": "romeo-fifteen-hotel-foxtrot", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:51:21.937Z", "stopped": "2025-06-15T11:51:22.175Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d22574bf8f44490f85bd4470ae50a403", "fields": {"name": "eighteen-mississip<PERSON>-aspen-happy", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:11:09.431Z", "stopped": "2025-06-16T00:11:09.571Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d32bcefd480e4a10bb755c1122788440", "fields": {"name": "snake-burger-march-north", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:06:17.143Z", "stopped": "2025-06-15T14:06:17.372Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d3928746a2434ee981f0a1f83ebb31df", "fields": {"name": "magnesium-fish-cola-johnny", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:41:24.186Z", "stopped": "2025-06-15T06:41:24.281Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d3de2d8f4a2a49338a8330b2959f0d00", "fields": {"name": "pip-bravo-minnesota-april", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:16:29.809Z", "stopped": "2025-06-15T23:16:30.046Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d6b7899204fa4d7bb3e6e1c6257d67e5", "fields": {"name": "lion-six-king-ten", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:01:12.575Z", "stopped": "2025-06-15T19:01:12.808Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d6d7002202ca4e38890c8841fb985bfe", "fields": {"name": "cat-sad-four-single", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:11:21.650Z", "stopped": "2025-06-15T17:11:21.801Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d87ed433455c43a09aa25b4bcd8f9ae3", "fields": {"name": "failed-wyoming-cola-eight", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:46:25.076Z", "stopped": "2025-06-15T06:46:25.359Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d8a395cf16804819b710ec8829826eef", "fields": {"name": "carolina-sink-seventeen-colorado", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:11:18.039Z", "stopped": "2025-06-15T14:11:18.125Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d8d697214c184318b93c9c24f8340b91", "fields": {"name": "red-avocado-eleven-green", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:41:31.821Z", "stopped": "2025-06-15T20:41:31.984Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d919cfaefc63445eb80b1022546cbdca", "fields": {"name": "october-maryland-maine-freddie", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:21:11.154Z", "stopped": "2025-06-16T00:21:11.330Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d972e8d5cf564e0988768f4f923e0d2a", "fields": {"name": "music-thirteen-emma-minnesota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:16:30.179Z", "stopped": "2025-06-15T15:16:30.277Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "da50239487834ef7a83537f755c77421", "fields": {"name": "eleven-rugby-high-sink", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:31:22.340Z", "stopped": "2025-06-15T06:31:22.457Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "dc405ae4ba284265971860b8717fe5fa", "fields": {"name": "carpet-quebec-grey-bakerloo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:51:14.384Z", "stopped": "2025-06-15T13:51:14.476Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "decc7a94bc7e4d369dff68bfadf27588", "fields": {"name": "yankee-river-quebec-ohio", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:26:10.232Z", "stopped": "2025-06-15T21:26:10.412Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "df3c9675fdb44672afa24c1093761a9c", "fields": {"name": "ack-louisiana-fruit-india", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:56:15.570Z", "stopped": "2025-06-15T21:56:15.760Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "df3e7fed883946ecb33509f092c204fc", "fields": {"name": "kitten-bulldog-magazine-oscar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T13:36:11.607Z", "stopped": "2025-06-15T13:36:11.901Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "df880fbd364046f9ace32969c7901125", "fields": {"name": "uncle-sixteen-avocado-india", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:31:12.861Z", "stopped": "2025-06-16T00:31:13.032Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "dfe1174f44d641e7894da2e3d8b51360", "fields": {"name": "iowa-don-maryland-california", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T14:21:19.907Z", "stopped": "2025-06-15T14:21:20.006Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e04a6002823d40ef8b490b1942486a80", "fields": {"name": "six-bacon-spaghetti-timing", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:36:30.363Z", "stopped": "2025-06-15T12:36:30.524Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e1345592abb040c7a10a96c902bb8a99", "fields": {"name": "batman-nineteen-white-oxygen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T23:31:32.467Z", "stopped": "2025-06-15T23:31:32.544Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e1922f4799764b77b2a7ecc29b4b6f61", "fields": {"name": "bulldog-stairway-eight-thirteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:21:20.569Z", "stopped": "2025-06-15T06:21:20.661Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e2154f4d37cd45229f8ac9e43e957fa7", "fields": {"name": "william-nitrogen-east-kitten", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T21:11:07.466Z", "stopped": "2025-06-15T21:11:07.633Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e2582b2882894ba7beb1f7852f6ae447", "fields": {"name": "river-uniform-louisiana-undress", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:01:09.022Z", "stopped": "2025-06-15T08:01:09.196Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e25efaea6f494070be6f87f8d9912b48", "fields": {"name": "paris-black-pennsylvania-comet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:31:29.910Z", "stopped": "2025-06-15T20:31:30.116Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e393a9a129f949c1b4048cff2e10dfef", "fields": {"name": "leopard-hamper-seventeen-autumn", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T06:56:26.922Z", "stopped": "2025-06-15T06:56:27.204Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e3bc5991b42143c0b0816847a7526db7", "fields": {"name": "nebraska-black-california-dakota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:11:18.312Z", "stopped": "2025-06-15T22:11:18.415Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e42af343d66d453e943e81a395d255f6", "fields": {"name": "low-saturn-mexico-two", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:16:20.635Z", "stopped": "2025-06-16T01:16:20.912Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e73ed101a4794ae9817d24a2e7044cad", "fields": {"name": "arkansas-low-lion-alanine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:51:36.659Z", "stopped": "2025-06-15T15:51:36.917Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e7f917bf3c844c3da00af38721fd3b62", "fields": {"name": "queen-sixteen-thirteen-maryland", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:46:17.405Z", "stopped": "2025-06-15T08:46:17.512Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e837bb2178a64b5b898fdce84be2712d", "fields": {"name": "finch-solar-delta-william", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T08:31:14.609Z", "stopped": "2025-06-15T08:31:14.898Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e83cf2affdcb4cd5a534feb2fb46d8df", "fields": {"name": "michigan-pip-texas-robert", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T05:21:09.433Z", "stopped": "2025-06-15T05:21:09.617Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ea93fc12f2aa4edf947c22d5323f51e7", "fields": {"name": "nebraska-sixteen-carbon-robin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:21:16.360Z", "stopped": "2025-06-15T19:21:16.628Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "eb72f429a32b4d04ab69fa1d9fa0a6b7", "fields": {"name": "fillet-september-charlie-cup", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:11:10.352Z", "stopped": "2025-06-15T16:11:10.590Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ed77fe2190014e419098d4eea4575838", "fields": {"name": "finch-north-delta-moon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:06:18.882Z", "stopped": "2025-06-16T01:06:19.159Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ef0e2f3c2b7545d3975fb1aeb2d54029", "fields": {"name": "west-six-sweet-autumn", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T12:21:27.539Z", "stopped": "2025-06-15T12:21:27.757Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ef59f8222c9f43a6aab669415ff1a46b", "fields": {"name": "juliet-green-don-idaho", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T20:46:32.810Z", "stopped": "2025-06-15T20:46:32.963Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f0639713f50340bd98a9593cd1d6b3a6", "fields": {"name": "blossom-asparagus-glucose-batman", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:51:22.233Z", "stopped": "2025-06-15T19:51:22.493Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f09e615b8a2f4271b89a56aa74791c8e", "fields": {"name": "glucose-music-thirteen-november", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:16:33.998Z", "stopped": "2025-06-15T18:16:34.235Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f107e1e9a3e349e19d85620a86a129d8", "fields": {"name": "zebra-uranus-kentucky-summer", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:11:14.485Z", "stopped": "2025-06-15T11:11:14.686Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f18ffd76b72a4312a6a21b40f2259490", "fields": {"name": "william-social-hotel-stairway", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:46:28.225Z", "stopped": "2025-06-15T17:46:28.310Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f27ebc0522944cc7b27fabf83894371e", "fields": {"name": "double-eleven-north-maryland", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T18:51:10.670Z", "stopped": "2025-06-15T18:51:10.869Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f3d95a0d18ec49e193bf5187da03a5f4", "fields": {"name": "louisiana-helium-wyoming-mexico", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T00:26:12.006Z", "stopped": "2025-06-16T00:26:12.221Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f43e62d91a344fc6b66af006e2fc0b68", "fields": {"name": "kilo-oregon-colorado-louisiana", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T10:21:35.226Z", "stopped": "2025-06-15T10:21:35.321Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f456a1d24b744417860fa924d2eea4b9", "fields": {"name": "speaker-juliet-alpha-carolina", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T15:01:27.371Z", "stopped": "2025-06-15T15:01:27.659Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f5fc351554f845119415c1bc4c281600", "fields": {"name": "cold-wisconsin-yankee-king", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-16T01:26:22.328Z", "stopped": "2025-06-16T01:26:22.422Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f6e1e1bd26974562a9917c7a12527e5a", "fields": {"name": "football-music-emma-indigo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T22:31:21.865Z", "stopped": "2025-06-15T22:31:21.967Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f7095b80ba9b4d7785406ec721e5e440", "fields": {"name": "charlie-bulldog-juliet-hydrogen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T19:11:14.449Z", "stopped": "2025-06-15T19:11:14.550Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f795f82256174c12a8192cb8fc045303", "fields": {"name": "red-carbon-charlie-oscar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T17:06:20.716Z", "stopped": "2025-06-15T17:06:20.984Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f8eca2d4a7b64f73b8c6b934991e6694", "fields": {"name": "illinois-crazy-whiskey-lake", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:26:13.167Z", "stopped": "2025-06-15T16:26:13.382Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "fcea41334b7e4858a0701a01245d2573", "fields": {"name": "indigo-charlie-snake-dakota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T16:31:14.100Z", "stopped": "2025-06-15T16:31:14.273Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "fd20e99418ce4885974e6d726c13b1b5", "fields": {"name": "nuts-bulldog-single-lithium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:41:20.095Z", "stopped": "2025-06-15T11:41:20.363Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "feedc033fb1140a99ceda674449b6925", "fields": {"name": "timing-alaska-oklahoma-moon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-15T11:06:13.563Z", "stopped": "2025-06-15T11:06:13.686Z", "success": true, "attempt_count": 1}}, {"model": "django_q.schedule", "pk": 1, "fields": {"name": null, "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "()", "kwargs": "{}", "schedule_type": "I", "minutes": 5, "repeats": -1, "next_run": "2025-06-16T01:46:06.836Z", "cron": null, "task": "61047309ccd34a86a0ff02135fb6343c", "cluster": null, "intended_date_kwarg": null}}, {"model": "shop.minecraftserver", "pk": 1, "fields": {"name": "ucraft-main", "display_name": "uCraft", "ip": "***********", "port": 25565, "domain": "play.ucraft.ir", "rcon_port": 25550, "query_port": 26577, "proxy": true, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 2, "fields": {"name": "ucraft-lobby", "display_name": "Game Lobby & Mini Games", "ip": "***********", "port": 25557, "domain": null, "rcon_port": 25551, "query_port": 25557, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 3, "fields": {"name": "ucraft-survival", "display_name": "Survival", "ip": "***********", "port": 22557, "domain": null, "rcon_port": 22552, "query_port": 22557, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 4, "fields": {"name": "ucraft-bedrwars", "display_name": "BedWars", "ip": "***********", "port": 22558, "domain": null, "rcon_port": 22553, "query_port": 22558, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.downloadlink", "pk": 1, "fields": {"platform": "windows", "url": "https://dl2.tlauncher.org/f.php?f=files%2FTLauncher-Installer-1.8.0.exe", "enabled": true, "published": true}}, {"model": "shop.downloadlink", "pk": 2, "fields": {"platform": "android", "url": "https://dl2.tlauncher.org/f.php?f=files%2FTLauncher-Installer-1.8.0.exe", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 1, "fields": {"name": "Survival", "display_name": "Survival", "description": "<PERSON><PERSON> hichi", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 2, "fields": {"name": "BedWars", "display_name": "BedWars", "description": "Faghat BedWars", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 3, "fields": {"name": "Ranks", "display_name": "Ranks", "description": "", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 1, "fields": {"name": "<PERSON><PERSON>", "display_name": "Raiden", "description": "", "image": "images-57b5d356c3fb4e319c517f73a6fcc68c.jpg", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 2, "fields": {"name": "<PERSON>", "display_name": "<PERSON>", "description": "", "image": "minecraft-removed-d5581f024afe40c783f5a6d5f43fe705.webp", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 3, "fields": {"name": "SinaZK", "display_name": "SinaZK", "description": "3 sale az tehran", "image": "minecraft-banner-figures-f8cd5936a9e94ba3b5f22fc51fd75cd7.png", "enabled": true, "published": true}}, {"model": "shop.item", "pk": 4, "fields": {"name": "rank-iron-1m", "display_name": "رنک آیرون - یک ماهه", "description": "تمامی گیم مود ها\r\n\r\n\r\n- نمایش رنک آیرون در چت و لیست پلیر ها (تب لیست)\r\n- دریافت رول مخصوص آیرون در سرور دیسکورد تیرکس ماین\r\n\r\n\r\nلابی ها\r\n\r\n\r\n- گرفتن ۵۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک آیرون\r\n- تخفیف ۱۰٪ روی تمام آیتم های Cosmetic Menu\r\n\r\n\r\nسروایول\r\n\r\n\r\n- قابلیت جوین دادن به سروایول بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت کلیم کردن تا 18 چانک\r\n- قابلیت شخصی کردن 5 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 3 نفر به وسایل شخصی سازی شده\r\n- قابلیت گذاشتن 7 عدد هاپر در هر چانک\r\n- قابلیت گذاشتن 15 عدد چست در هر چانک\r\n- قابلیت گذاشتن 15 عدد آیتم رداستونی در هر چانک\r\n- قابلیت انتخاب ۲ تا شغل (Job) همزمان\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n-قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاسکای بلاک\r\n\r\n\r\n- قابلیت جوین دادن به اسکای بلاک بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت شخصی کردن 5 بلاک\r\n- قابلیت شخصی کردن 3 ماب\r\n- قابلیت باز کردن پت شاپ\r\n- قابلیت تراست کردن 3 نفر به وسایل شخصی سازی شده\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت گذاشتن 2 کاستوم انچنت روی آیتم با دستورce/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n- قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاف اف ای\r\n\r\n\r\n- دسترسی به کازمتیک های مربوط به رنک آیرون\r\n- داشتن تخفیف مخصوص رنک آیرون بر روی کازمتیک ها\r\n\r\n\r\nکریتیو\r\n\r\n\r\n-قابلیت دیناید کردن 6 عدد پلیر از پلات\r\n-قابلیت ادد کردن 6 عدد پلیر به پلات\r\n-قابلیت تراست کردن 6 عدد پلیر به پلات", "image": "iron-rank-f638f65b45eb4662b32f1ffdbee80c54.webp", "category": 3, "price": 79000, "ucoin_price": 79, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 5, "fields": {"name": "rank-gold-1m", "display_name": "رن<PERSON> گلد - ۱ ماهه", "description": "تمامی گیم مود ها\r\n\r\n\r\n- نمایش رنک گلد در چت و لیست پلیر ها (تب لیست)\r\n- دریافت رول مخصوص گلد در سرور دیسکورد تیرکس ماین\r\n\r\n\r\nلابی ها\r\n\r\n\r\n- گرفتن ۵۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک آیرون\r\n- گرفتن ۱۰۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک گولد\r\n- قابلیت باز کردن ۲۰ تا Mystery Box به طور همزمان\r\n- قابلیت پرواز کردن با کامند /fly\r\n- تخفیف ۲۰٪ روی تمام آیتم های Cosmetic Menu\r\n- قابلیت فرستادن پیام رنگی تو چت\r\n\r\n\r\nسروایول\r\n\r\n\r\n- قابلیت پرواز کردن با کامند /fly\r\n- قابلیت /smithingtable\r\n- قابلیت گرفتن کیت گولد\r\n- قابلیت جوین دادن به سروایول بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت کلیم کردن تا 24 چانک\r\n- قابلیت شخصی کردن 7 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 4 نفر به وسایل شخصی سازی شده\r\n- قابلیت گذاشتن 7 عدد هاپر در هر چانک\r\n- قابلیت گذاشتن 15 عدد چست در هر چانک\r\n- قابلیت گذاشتن 15 عدد آیتم رداستونی در هر چانک\r\n- قابلیت انتخاب 3 تا شغل (Job) همزمان\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت انتخاب 3 تا home با دستور sethome/\r\n-قابلیت فروش همزمان 3 آیتم با دستور ah/\r\n\r\n\r\nاسکای بلاک\r\n\r\n\r\n- قابلیت پرواز کردن با کامند /fly\r\n- قابلیت جوین دادن به اسکای بلاک بعد از پر شدن سرور\r\n- قابلیت /smithingtable\r\n- قابلیت گرفتن کیت گولد\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت شخصی کردن 7 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 4 نفر به وسایل شخصی سازی شده\r\n- قابلیت باز کردن پت شاپ\r\n- قابلیت باز کردن شاپ farming\r\n- قابلیت باز کردن شاپ mobdrops\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت گذاشتن 2 کاستوم انچنت روی آیتم با دستورce/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n- قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاف اف ای\r\n\r\n\r\n- دسترسی به کازمتیک های مربوط به رنک گولد\r\n- داشتن تخفیف مخصوص رنک گولد بر روی کازمتیک ها\r\n- دسترسی به کازمتیک های مربوط به رنک آیرون\r\n- داشتن تخفیف مخصوص رنک آیرون بر روی کازمتیک ها\r\n\r\n\r\nکریتیو\r\n\r\n\r\n-قابلیت دیناید کردن 7 عدد پلیر از پلات\r\n-قابلیت ادد کردن 7 عدد پلیر به پلات\r\n-قابلیت تراست کردن 7 عدد پلیر به پلات", "image": "gold-rank-2c1f09177f874373ab0d74d15f21e994.webp", "category": 3, "price": 149000, "ucoin_price": 149, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 6, "fields": {"name": "rank-dia-1m", "display_name": "رنک دیاموند - ۱ ماهه", "description": "", "image": "diamond-rank-fc49ff842281487ba00864a4cde9f448.webp", "category": 3, "price": 189000, "ucoin_price": 189, "commands": "say hello", "revoke_commands": "say bye", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 7, "fields": {"name": "rank-emral-1m", "display_name": "رن<PERSON> امرالد - ۱ ماهه", "description": "", "image": "emerald-rank-6b4e5ddfe81b42a0b8ff030c9abfbeb2.webp", "category": 3, "price": 349000, "ucoin_price": 349, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 8, "fields": {"name": "rank-fal-20d", "display_name": "رنک فال - ۲۰ روز", "description": "", "image": "fall-rank-01f58134c7a64f2aa9b9deb1521fa50a.webp", "category": 3, "price": 569000, "ucoin_price": 569, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 20, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 9, "fields": {"name": "survival-key-5k-common", "display_name": "۵ کلید Common", "description": "شما با خرید این محصول پنج عدد کلید جعبه \" Common \" بدست میارید\r\n\r\nجزئیات : این جعبه شامل آیتم های زیر هست:\r\n15% شانس برای گرفتن یک عدد Common Helmet\r\n15% شانس برای گرفتن یک عدد Common Chestplate\r\n15% شانس برای گرفتن یک عدد Common Leggings\r\n15% شانس برای گرفتن یک عدد Common Boots\r\n15% شانس برای گرفتن یک عدد Common Sword\r\n15% شانس برای گرفتن یک عدد Common Pickaxe\r\n15% شانس برای گرفتن یک عدد Common Axe\r\n15% شانس برای گرفتن یک عدد Common Shovel\r\n15% شانس برای گرفتن یک عدد Common Bow\r\n20% شانس برای گرفتن 32 عدد Gold Block\r\n25% شانس برای گرفتن 32 عدد Iron Block\r\n10% شانس برای گرفتن 16 عدد Diamond Ore\r\n10% شانس برای گرفتن 16 عدد Emerald Ore\r\n20% شانس برای گرفتن 5 عدد تخم Creeper\r\n15% شانس برای گرفتن 10 عدد تخم Blaze\r\n5% شانس برای گرفتن یک عدد Chicken Spawner\r\n5% شانس برای گرفتن یک عدد Sheep Spawner\r\n20% شانس برای گرفتن 16 عدد Ender Pearl\r\n30% شانس برای گرفتن 1000 XP\r\n25% شانس برای گرفتن 2500 XP\r\n\r\nنحوه استفاده : برای استفاده کردن از این محصول کافیست درگیم مود سروایول به warp crates برید و با در دست گرفتن کلید تون روی جعبه مورد نظر راست کلیک کنید", "image": "common-************************************.webp", "category": 1, "price": 58000, "ucoin_price": 58, "commands": "", "revoke_commands": "", "minecraft_server": null, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 10, "fields": {"name": "survival-key-5k-rare", "display_name": "۵ کلید Rare", "description": "", "image": "rare-************************************.webp", "category": 1, "price": 99000, "ucoin_price": 99, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 11, "fields": {"name": "survival-key-5k-money", "display_name": "۵ کلید Money", "description": "", "image": "money-************************************.webp", "category": 1, "price": 147000, "ucoin_price": 147, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 12, "fields": {"name": "survival-cube-5*5", "display_name": "کیوب 5x5 Survival", "description": "قابلیت لینک کردن کیوب (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Sell all (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Rebuild\r\nقابلیت Smelt all (نیازمند رنک تیرکس یا بالاتر)\r\nقابلیت Compressor\r\nقابلیت Storage\r\nقابلیت Upgrades\r\n\r\nقابلیت آپگرید کردن Air percentage تا 3 لول\r\nقابلیت آپگرید کردن Quality تا 3 لول\r\nقابلیت آپگرید کردن Storage تا 3 لول", "image": "SrMineCube5-image-2023-01-05-8299609f21f84fcebdb6e5cb94ff2adc.webp", "category": 1, "price": 146000, "ucoin_price": 146, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 13, "fields": {"name": "survival-cube-15*15", "display_name": "کیوب 15x15 Survival", "description": "قابلیت لینک کردن کیوب (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Sell all (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Rebuild\r\nقابلیت Smelt all (نیازمند رنک تیرکس یا بالاتر)\r\nقابلیت Compressor\r\nقابلیت Storage\r\nقابلیت Upgrades\r\n\r\nقابلیت آپگرید کردن Air percentage تا 4 لول\r\nقابلیت آپگرید کردن Quality تا 3 لول\r\nقابلیت آپگرید کردن Storage تا 3 لول", "image": "SrMineCube15-image-2023-01-05-501c34af4d8a49d8ac1c1e97b2f2f3fa.webp", "category": 1, "price": 299000, "ucoin_price": 299, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 14, "fields": {"name": "survival-key-5k-spawner", "display_name": "۵ کلید Spawner", "description": "", "image": "spawner-************************************.webp", "category": 1, "price": 189000, "ucoin_price": 188, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 15, "fields": {"name": "survival-key-5k-legendary", "display_name": "۵ ک<PERSON><PERSON>د Legendary", "description": "", "image": "legendary-************************************.webp", "category": 1, "price": 199000, "ucoin_price": 199, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 16, "fields": {"name": "survival-key-5k-aegis", "display_name": "۵ ک<PERSON><PERSON><PERSON>is", "description": "", "image": "immortal-************************************.webp", "category": 1, "price": 493000, "ucoin_price": 493, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 17, "fields": {"name": "bedwars-private-1m", "display_name": "بازی خصوصی - ۱ ماهه", "description": "دسترسی یک ماهه به بازی های خصوصی", "image": "battlepass_GDWgCZn-35a9a3bd16d94b5a864fd247a2b24a3c.webp", "category": 2, "price": 319000, "ucoin_price": 319, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 18, "fields": {"name": "bedwars-token-50k", "display_name": "۵۰k سکه", "description": "", "image": "bw-50k_MqSaqxy-535e47e0fdfa49cea49f37733850a68d.webp", "category": 2, "price": 99000, "ucoin_price": 99, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 19, "fields": {"name": "bedwars-token-100k", "display_name": "100K tokens", "description": "", "image": "bw-100k_BLvlAH5-298d2e0570c94bfeab1b352b07d3f56b.webp", "category": 2, "price": 189000, "ucoin_price": 186, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 20, "fields": {"name": "bedwars-token-200k", "display_name": "200K tokens", "description": "", "image": "bw-200k_RUHadlg-131b82aa5469447f918123928e91fc42.webp", "category": 2, "price": 339000, "ucoin_price": 339, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.purchase", "pk": 14, "fields": {"minecraft_username": "Sinazk", "mobile_number": "+989127379177", "state": "failed", "referrer": null, "created_at": "2025-05-30T10:57:32.606Z", "payment_succeeded_at": null, "ref_id": null, "authority": "A0000000000000000000000000006qy3ej5e", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 15, "fields": {"minecraft_username": "Sinazk", "mobile_number": "+989127379177", "state": "timeout", "referrer": null, "created_at": "2025-05-30T22:43:38.126Z", "payment_succeeded_at": null, "ref_id": null, "authority": "A000000000000000000000000000xqxnrwjz", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 16, "fields": {"minecraft_username": "Sinazk", "mobile_number": "09127379177", "state": "timeout", "referrer": null, "created_at": "2025-05-30T22:43:47.324Z", "payment_succeeded_at": null, "ref_id": null, "authority": "A000000000000000000000000000de3q8pm5", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 17, "fields": {"minecraft_username": "raiden", "mobile_number": "+989024500575", "state": "failed", "referrer": null, "created_at": "2025-05-31T05:03:06.935Z", "payment_succeeded_at": null, "ref_id": null, "authority": "A0000000000000000000000000003zgrqno7", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 18, "fields": {"minecraft_username": "raiden", "mobile_number": "+989024500575", "state": "timeout", "referrer": null, "created_at": "2025-05-31T20:46:05.971Z", "payment_succeeded_at": null, "ref_id": "69168302", "authority": "A000000000000000000000000000oxwyz157", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 19, "fields": {"minecraft_username": "Sinazk", "mobile_number": "09127379177", "state": "timeout", "referrer": null, "created_at": "2025-05-31T20:50:13.093Z", "payment_succeeded_at": null, "ref_id": "32510431", "authority": "A000000000000000000000000000l325zdre", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 20, "fields": {"minecraft_username": "something", "mobile_number": "0902 234 2323", "state": "timeout", "referrer": null, "created_at": "2025-06-10T23:36:07.923Z", "payment_succeeded_at": null, "ref_id": "33428270", "authority": "A000000000000000000000000000mr77dnn3", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 21, "fields": {"minecraft_username": "something", "mobile_number": "0902 234 2323", "state": "failed", "referrer": null, "created_at": "2025-06-10T23:41:16.292Z", "payment_succeeded_at": null, "ref_id": "51543906", "authority": "A000000000000000000000000000vgxxerrv", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 22, "fields": {"minecraft_username": "something", "mobile_number": "0902 234 2323", "state": "timeout", "referrer": null, "created_at": "2025-06-10T23:57:21.266Z", "payment_succeeded_at": null, "ref_id": "70279083", "authority": "A000000000000000000000000000y5vvenq7", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 23, "fields": {"minecraft_username": "something", "mobile_number": "+989024500575", "state": "timeout", "referrer": null, "created_at": "2025-06-12T16:01:03.491Z", "payment_succeeded_at": null, "ref_id": "82073561", "authority": "A0000000000000000000000000007z7yln12", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 24, "fields": {"minecraft_username": "something", "mobile_number": "+989024248585", "state": "timeout", "referrer": 2, "created_at": "2025-06-12T16:02:41.568Z", "payment_succeeded_at": null, "ref_id": "63813930", "authority": "A000000000000000000000000000z2n8r3wz", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchaseitem", "pk": 1, "fields": {"purchase": 20, "item": 7, "quantity": 1, "created_at": "2025-06-10T23:36:07.928Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 2, "fields": {"purchase": 20, "item": 6, "quantity": 1, "created_at": "2025-06-10T23:36:07.936Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 3, "fields": {"purchase": 21, "item": 7, "quantity": 1, "created_at": "2025-06-10T23:41:16.296Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 4, "fields": {"purchase": 21, "item": 6, "quantity": 1, "created_at": "2025-06-10T23:41:16.301Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 5, "fields": {"purchase": 22, "item": 7, "quantity": 1, "created_at": "2025-06-10T23:57:21.271Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 6, "fields": {"purchase": 22, "item": 6, "quantity": 1, "created_at": "2025-06-10T23:57:21.277Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 7, "fields": {"purchase": 22, "item": 15, "quantity": 1, "created_at": "2025-06-10T23:57:21.281Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 8, "fields": {"purchase": 22, "item": 14, "quantity": 1, "created_at": "2025-06-10T23:57:21.289Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 9, "fields": {"purchase": 23, "item": 11, "quantity": 1, "created_at": "2025-06-12T16:01:03.505Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 10, "fields": {"purchase": 23, "item": 14, "quantity": 1, "created_at": "2025-06-12T16:01:03.513Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 11, "fields": {"purchase": 23, "item": 15, "quantity": 1, "created_at": "2025-06-12T16:01:03.517Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 12, "fields": {"purchase": 24, "item": 14, "quantity": 1, "created_at": "2025-06-12T16:02:41.572Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 13, "fields": {"purchase": 24, "item": 12, "quantity": 1, "created_at": "2025-06-12T16:02:41.578Z", "expires_at": null, "subscription_status": "onetime"}}, {"model": "shop.purchaseitem", "pk": 14, "fields": {"purchase": 24, "item": 11, "quantity": 1, "created_at": "2025-06-12T16:02:41.584Z", "expires_at": null, "subscription_status": "onetime"}}]